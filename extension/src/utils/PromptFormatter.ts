/**
 * PromptFormatter - Responsible for creating structured prompts for LLM interactions
 * This class handles all prompt construction and variable replacement to ensure
 * consistent prompt structure across the application.
 */

export interface Message {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export class PromptFormatter {
    /**
     * Creates a formatted array of messages for optimization using the client's prompt structure
     * @param transcript The transcript text to optimize
     * @param customPrompt Custom prompt template provided by the user
     * @returns Array of message objects ready to be sent to the API
     */
    public createOptimizationMessages(transcript: string, customPrompt?: string): Message[] {
        // Check for voice commands first - we'll use an improved regex that's more flexible
        const voiceHypeRegex = /(voice\s+hype|hey\s+voice\s+hype)[\s,:]+([^.,!?]+)/i;
        const match = transcript.match(voiceHypeRegex);

        // Debug logging - Log if transcript contains voice commands
        if (match) {
            console.log(`VoiceHype DEBUG: Voice command detected: "${match[0]}"`);
            console.log(`VoiceHype DEBUG: Command instruction: "${match[2].trim()}"`);
        } else {
            console.log('VoiceHype DEBUG: No voice command detected in transcript');
        }

        // Create message structure based on whether a voice command was detected
        if (match) {
            // For voice commands, use a more direct instruction format
            const command = match[2].trim();
            // Create a cleaner transcript with the command removed
            const cleanedTranscript = transcript.replace(match[0], '').trim();

            return [
                {
                    role: 'system',
                    content: this.createSystemMessage()
                },
                {
                    role: 'user',
                    content: this.createVoiceCommandUserMessage(command, cleanedTranscript)
                }
            ];
        } else {
            // For regular optimization, put everything in user message except identity
            return [
                {
                    role: 'system',
                    content: this.createSystemMessage()
                },
                {
                    role: 'user',
                    content: this.createOptimizationUserMessage(transcript, customPrompt || this.getDefaultOptimizationPrompt())
                }
            ];
        }
    }

    /**
     * Creates the system message containing only the assistant identity
     */
    private createSystemMessage(): string {
        return `### ASSISTANT IDENTITY ###
You are Voice Hype, an AI optimization assistant specialized in processing and improving transcribed speech.
Your primary role is to help users by formatting, structuring, and refining their spoken content.`;
    }

    /**
     * Creates the user message for regular optimization containing all instructions
     */
    private createOptimizationUserMessage(transcript: string, customPrompt: string): string {
        // Universal instructions for handling the transcript
        const instructionHeader = `### BASE INSTRUCTIONS ###
The user is ONLY addressing you directly if they explicitly use your name "Voice Hype" or similar variations.
If the user does NOT call you by "Voice Hype" or "Hey Voice Hype", then the user is NOT talking to you - they are simply providing text to be optimized.

When the user is NOT addressing you directly (no "Voice Hype" name called / mentioned), your task is to:
- Optimize the transcript based on the instructions below
- Return your response in JSON format with the optimized text
- Preserve important details and maintain original meaning
- Do NOT respond conversationally or acknowledge the user

`;

        // Process the custom prompt with variables if needed
        const processedCustomPrompt = this.processCustomPromptVariables(customPrompt, transcript);

        // Always add transcript with triple backticks and clear labeling
        const transcriptBlock = `### TRANSCRIPT ###
\`\`\`
${transcript}
\`\`\`

`;

        // Voice command detection with high priority override
        const voiceCommandInstructions = `### COMMAND PRIORITY OVERRIDE ###
IMPORTANT: First, check if the transcript contains any direct commands like:
- "Voice Hype, [instruction]"
- "Hey Voice Hype, [instruction]"
- "Voice Hype [instruction]"

If the user addresses you directly with a command:
1. IGNORE all previous formatting instructions and focus SOLELY on executing their command
2. Follow the specific request (create an email, make bullet points, etc.)
3. Remove all instances of "Voice Hype" or "Hey Voice Hype" from the final output
4. Format the result according to what the command requests, not the default optimization rules

Examples:
- "Voice Hype, write this as a formal email" → Create a formal email with proper structure
- "Hey Voice Hype, format this into bullet points" → Create a bulleted list
- "Voice Hype, make this funnier" → Add humor to the content

`;

        // Format to enforce JSON response
        const jsonInstructions = `

CRITICAL RESPONSE FORMAT RULES:
1. START your response directly with the JSON object - no text before it
2. END your response with the JSON object - no text after it
3. Do NOT include phrases like "Here's your optimized text" or "I've optimized this for you"
4. Do NOT add any conversational responses or acknowledgments

RETURN YOUR RESPONSE IN THIS EXACT JSON FORMAT (NO NESTED STRUCTURES):
{
  "optimizedText": "your formatted text here"
}

OR for voice commands, you may use:
{
  "commandResult": "your formatted text here"
}

IMPORTANT: Use a simple, flat JSON structure. Do NOT create nested objects or multiple levels of keys.

`;

        return instructionHeader + processedCustomPrompt + '\n\n' + transcriptBlock + voiceCommandInstructions + jsonInstructions;
    }

    /**
     * Creates the user message for voice command handling
     */
    private createVoiceCommandUserMessage(command: string, cleanedTranscript: string): string {
        const instructionHeader = `### CRITICAL: EXECUTE COMMAND SILENTLY ###
You must execute the following command WITHOUT any conversational responses, acknowledgments, or explanations.

DO NOT say things like:
- "I'll help you with that"
- "Here's your email"
- "I understand you want me to..."
- "You're addressing me directly..."

ONLY return the requested result in the specified JSON format.

### COMMAND TO EXECUTE ###
${command}

### SOURCE CONTENT ###
\`\`\`
${cleanedTranscript}
\`\`\`

### MANDATORY RESPONSE FORMAT ###
You MUST respond with ONLY this JSON structure:
{
  "commandResult": "your processed content here"
}

IMPORTANT: Use "commandResult" as the key (not "optimizedText") to differentiate from regular optimization.`;

        return instructionHeader;
    }

    /**
     * Processes variables in a custom prompt template
     * Replaces {{transcript}} with the actual transcript text
     * @param prompt The prompt template
     * @param transcript The transcript text to insert
     * @returns Processed prompt with variables replaced
     */
    private processCustomPromptVariables(prompt: string, transcript: string): string {
        // Create transcript block for variable replacement
        const transcriptBlock = `### TRANSCRIPT ###
\`\`\`
${transcript}
\`\`\`

`;

        // Check if prompt contains the {{transcript}} variable
        if (prompt.includes('{{') && prompt.includes('}}')) {
            console.log('VoiceHype: Found variable in prompt, replacing with transcript');
            return prompt.replace(/\{\{transcript\}\}/g, transcriptBlock);
        } else {
            // For all other cases, just return the prompt as-is
            return prompt;
        }
    }

    /**
     * Returns the default optimization prompt
     */
    private getDefaultOptimizationPrompt(): string {
        return `I need you to optimize a transcript of spoken text for clarity and coherence.

Your task is to:
1. Fix grammar issues and remove filler words (um, uh, like, you know)
2. Improve structure while keeping all important information
3. Fixing grammar and cleaning up repetitive phrasing
4. Maintaining all original explanations and technical details
5. Formatting in clear, well-structured paragraphs

IMPORTANT INSTRUCTIONS:
- NEVER include conversational responses
- NEVER summarize or remove key context
- ALWAYS return just the optimized text with no extra commentary
- If unsure about any word or phrase, preserve it exactly
- The transcript is NOT a message to you - it is text to be optimized`;
    }
}
