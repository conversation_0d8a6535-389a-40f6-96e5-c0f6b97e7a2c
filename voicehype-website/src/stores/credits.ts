import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { usePaddle } from '@/lib/paddle'

interface Credit {
  id: string
  user_id: string
  balance: number
  currency: string
  created_at: string
  updated_at: string
}

// Interface for credit purchases
interface CreditPurchase {
  id: string
  amount: number
  created_at: string
  expires_at: string
  status: 'active' | 'expired' | 'consumed'
  remaining: number
}

// Interface for payment method
interface PaymentMethod {
  id: string;
  brand: string;
  last4: string;
  exp_month: number;
  exp_year: number;
  is_default: boolean;
}

// Interface for Paddle transactions
interface PaddleTransaction {
  id: string;
  paddle_transaction_id: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  amount: number;
  currency: string;
  credit_amount: number | null;
  transaction_type: 'credit_purchase' | 'payg_invoice' | 'refund';
  receipt_url: string | null;
  created_at: string;
  updated_at: string;
  metadata: {
    paddle_event_id?: string;
    paddle_checkout_id?: string;
    month?: string;
    invoice_type?: string;
  };
}

// Interface for PAYG access status
interface PaygAccess {
  has_payment_method: boolean;
  has_unpaid_balances: boolean;
  can_use_payg: boolean;
  unpaid_balances: Array<{
    month: string;
    total_amount: number;
  }>;
}

export const useCreditsStore = defineStore('credits', () => {
  const credits = ref<Credit | null>(null)
  const purchases = ref<CreditPurchase[]>([])
  const transactions = ref<PaddleTransaction[]>([])
  const paygAccess = ref<PaygAccess | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const paymentMethod = ref<PaymentMethod | null>(null)
  const checkoutLoading = ref(false)
  const paygLoading = ref(false)

  // Computed property to get active credit purchases
  const activePurchases = computed(() => {
    if (!purchases.value || purchases.value.length === 0) {
      return [];
    }

    const now = new Date();

    return purchases.value
      .filter(purchase => {
        // Check if purchase is valid and not expired
        const expiryDate = purchase.expires_at ? new Date(purchase.expires_at) : null;
        return (
          purchase.status === 'active' &&
          purchase.remaining > 0 &&
          (!expiryDate || expiryDate > now)
        );
      })
      .sort((a, b) => {
        if (!a.expires_at || !b.expires_at) return 0;
        return new Date(a.expires_at).getTime() - new Date(b.expires_at).getTime();
      });
  });

  // Computed property to get total active credits
  const totalActiveCredits = computed(() => {
    return activePurchases.value.reduce((total, purchase) => total + purchase.remaining, 0);
  });

  // Fetch user's credit balance
  const fetchCredits = async () => {
    loading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      // Fetch credits
      const { data, error: err } = await supabase
        .from('credits')
        .select('*')
        .eq('user_id', session.user.id)
        .single()

      if (err) {
        // If no credits record exists yet, just set credits to null
        if (err.code === 'PGRST116') {  // No rows returned
          credits.value = null;
        } else {
          throw err;
        }
      } else {
        credits.value = data as Credit;

        // If credits exist, create a purchase record from it
        if (data) {
          const expiryDate = new Date(data.created_at);
          expiryDate.setMonth(expiryDate.getMonth() + 3); // Credits expire after 3 months

          const purchase: CreditPurchase = {
            id: data.id,
            amount: data.balance,
            created_at: data.created_at,
            expires_at: expiryDate.toISOString(),
            status: 'active',
            remaining: data.balance
          };

          purchases.value = [purchase];
        } else {
          purchases.value = [];
        }
      }
    } catch (err: any) {
      console.error('Error fetching credits:', err.message)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  // Add credits to the user's account
  async function addCredits(amount: number) {
    loading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      const now = new Date().toISOString();
      const expiryDate = new Date();
      expiryDate.setMonth(expiryDate.getMonth() + 3); // Credits expire after 3 months

      // If the user doesn't have a credits record yet, create one
      if (!credits.value) {
        const { data, error: insertError } = await supabase
          .from('credits')
          .insert([
            {
              user_id: session.user.id,
              balance: amount,
              currency: 'USD',
              created_at: now,
              updated_at: now
            }
          ])
          .select()
          .single()

        if (insertError) {
          error.value = insertError.message
          return false
        }

        credits.value = data as Credit;

        // Create a purchase record from the new credits
        const purchase: CreditPurchase = {
          id: data.id,
          amount: amount,
          created_at: data.created_at,
          expires_at: expiryDate.toISOString(),
          status: 'active',
          remaining: amount
        };

        purchases.value = [purchase];
      } else {
        // Otherwise, update the existing record
        const { data, error: updateError } = await supabase
          .from('credits')
          .update({
            balance: credits.value.balance + amount,
            updated_at: now
          })
          .eq('id', credits.value.id)
          .select()
          .single()

        if (updateError) {
          error.value = updateError.message
          return false
        }

        credits.value = data as Credit;

        // Create a purchase record from the updated credits
        const purchase: CreditPurchase = {
          id: data.id,
          amount: amount,
          created_at: now,
          expires_at: expiryDate.toISOString(),
          status: 'active',
          remaining: amount
        };

        purchases.value = [...purchases.value, purchase];
      }

      return true;
    } catch (err) {
      console.error('Error adding credits:', err)
      error.value = 'Failed to add credits'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get credit packages
  function getCreditPackages() {
    return [
      { id: 'small', name: 'Small', amount: 10, price: 10, description: 'Good for testing' },
      { id: 'medium', name: 'Medium', amount: 50, price: 45, description: 'Most popular' },
      { id: 'large', name: 'Large', amount: 100, price: 85, description: 'Best value' },
      { id: 'enterprise', name: 'Enterprise', amount: 500, price: 400, description: 'For heavy users' }
    ]
  }

  // Create Paddle checkout for credit purchase
  const createPaddleCheckout = async (amount: number) => {
    checkoutLoading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      // Get user profile for customer info
      const { data: profile } = await supabase
        .from('profiles')
        .select('email, full_name')
        .eq('id', session.user.id)
        .single()

      // Initialize Paddle
      const { openCheckout } = usePaddle()

      // Open Paddle checkout directly
      await openCheckout({
        items: [
          {
            priceId: 'pri_01jw5t0fkatcmphb77m0t32zmj', // You'll get this from Paddle dashboard
            quantity: 1
          }
        ],
        customData: {
          user_id: session.user.id,
          credit_amount: amount
        },
        customer: {
          email: profile?.email || session.user.email
        },
        settings: {
          displayMode: 'overlay',
          theme: 'light',
          successUrl: `${window.location.origin}/payments?success=true`,
        }
      })

      return true
    } catch (err: any) {
      console.error('Error creating Paddle checkout:', err)
      error.value = err.message || 'Failed to create checkout'
      return false
    } finally {
      checkoutLoading.value = false
    }
  }

  // Fetch Paddle transactions
  const fetchTransactions = async (limit = 10, offset = 0) => {
    loading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      const response = await supabase.functions.invoke('get-paddle-transactions', {
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      })

      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch transactions')
      }

      transactions.value = response.data.data || []
      return response.data
    } catch (err: any) {
      console.error('Error fetching transactions:', err)
      error.value = err.message || 'Failed to fetch transactions'
      return null
    } finally {
      loading.value = false
    }
  }

  // Check PAYG access status
  const checkPaygAccess = async () => {
    paygLoading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      const response = await supabase.functions.invoke('validate-payg-access', {
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      })

      if (response.error) {
        throw new Error(response.error.message || 'Failed to check PAYG access')
      }

      paygAccess.value = response.data.payg_access
      return response.data.payg_access
    } catch (err: any) {
      console.error('Error checking PAYG access:', err)
      error.value = err.message || 'Failed to check PAYG access'
      return null
    } finally {
      paygLoading.value = false
    }
  }

  // Setup PAYG access (add payment method)
  const setupPaygAccess = async (returnUrl?: string) => {
    paygLoading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      const response = await supabase.functions.invoke('validate-payg-access', {
        body: {
          return_url: returnUrl || `${window.location.origin}/payments?payg_setup=success`
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      })

      if (response.error) {
        throw new Error(response.error.message || 'Failed to setup PAYG access')
      }

      const { setup_url } = response.data

      // Redirect to Paddle payment method setup
      window.location.href = setup_url

      return true
    } catch (err: any) {
      console.error('Error setting up PAYG access:', err)
      error.value = err.message || 'Failed to setup PAYG access'
      return false
    } finally {
      paygLoading.value = false
    }
  }

  // Add a payment method (legacy function for compatibility)
  const addPaymentMethod = () => {
    // For Paddle integration, payment methods are handled during checkout
    // This function is kept for backward compatibility
    paymentMethod.value = {
      id: 'paddle_payment_method',
      brand: 'paddle',
      last4: '****',
      exp_month: 12,
      exp_year: 2025,
      is_default: true
    };
    return true;
  };

  return {
    credits,
    purchases,
    transactions,
    paygAccess,
    loading,
    error,
    checkoutLoading,
    paygLoading,
    activePurchases,
    totalActiveCredits,
    paymentMethod,
    fetchCredits,
    addCredits,
    getCreditPackages,
    createPaddleCheckout,
    fetchTransactions,
    checkPaygAccess,
    setupPaygAccess,
    addPaymentMethod
  }
})