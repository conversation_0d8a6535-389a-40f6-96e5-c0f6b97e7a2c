// Paddle.js Integration for VoiceHype
// This file handles client-side Paddle integration

import { ref, readonly } from 'vue'

interface PaddleInstance {
  Environment: {
    set: (environment: 'sandbox' | 'production') => void
  }
  Setup: (options: { token: string }) => void
  Checkout: {
    open: (options: CheckoutOptions) => void
  }
  Update: (options: { token?: string }) => void
}

interface CheckoutOptions {
  items?: Array<{
    priceId: string
    quantity: number
  }>
  customData?: Record<string, any>
  customer?: {
    email?: string
  }
  settings?: {
    successUrl?: string
    theme?: 'light' | 'dark'
    locale?: string
    displayMode?: 'inline' | 'overlay'
    frameTarget?: string
    frameInitialHeight?: number
    frameStyle?: string
  }
}

declare global {
  interface Window {
    Paddle?: PaddleInstance
  }
}

class PaddleService {
  private isInitialized = false
  private isLoading = false
  private initPromise: Promise<void> | null = null

  /**
   * Initialize Paddle.js
   */
  async init(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    if (this.isLoading) {
      return this.initPromise!
    }

    this.isLoading = true
    this.initPromise = this.loadPaddle()

    try {
      await this.initPromise
      this.isInitialized = true
    } finally {
      this.isLoading = false
    }
  }

  /**
   * Load Paddle.js script and initialize
   */
  private async loadPaddle(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if Paddle is already loaded
      if (window.Paddle) {
        this.setupPaddle()
        resolve()
        return
      }

      // Create script element
      const script = document.createElement('script')
      script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js'
      script.async = true

      script.onload = () => {
        // Wait a bit for Paddle to be available
        setTimeout(() => {
          if (window.Paddle) {
            this.setupPaddle()
            resolve()
          } else {
            reject(new Error('Paddle failed to load'))
          }
        }, 100)
      }

      script.onerror = () => {
        reject(new Error('Failed to load Paddle script'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * Setup Paddle with environment and token
   */
  private setupPaddle(): void {
    if (!window.Paddle) {
      throw new Error('Paddle is not available')
    }

    const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox'
    const token = import.meta.env.VITE_PADDLE_CLIENT_TOKEN

    if (!token) {
      throw new Error('Paddle client token is not configured')
    }

    // Set environment
    window.Paddle.Environment.set(environment as 'sandbox' | 'production')

    // Initialize with token
    window.Paddle.Setup({ token })

    console.log(`Paddle initialized in ${environment} mode`)
  }

  /**
   * Open Paddle checkout
   */
  async openCheckout(options: CheckoutOptions): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      throw new Error('Paddle is not initialized')
    }

    // Set default options
    const defaultOptions: CheckoutOptions = {
      settings: {
        displayMode: 'overlay',
        theme: 'light',
        successUrl: `${window.location.origin}/payments?success=true`,
        ...options.settings
      },
      ...options
    }

    window.Paddle.Checkout.open(defaultOptions)
  }

  /**
   * Update Paddle configuration (useful for token updates)
   */
  async updateConfig(options: { token?: string }): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      throw new Error('Paddle is not initialized')
    }

    window.Paddle.Update(options)
  }

  /**
   * Check if Paddle is initialized
   */
  get initialized(): boolean {
    return this.isInitialized
  }

  /**
   * Get Paddle instance (for advanced usage)
   */
  get instance(): PaddleInstance | undefined {
    return window.Paddle
  }
}

// Create singleton instance
export const paddleService = new PaddleService()

// Vue composable for Paddle
export function usePaddle() {
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const init = async () => {
    if (isInitialized.value) return

    isLoading.value = true
    error.value = null

    try {
      await paddleService.init()
      isInitialized.value = true
    } catch (err: any) {
      error.value = err.message || 'Failed to initialize Paddle'
      console.error('Paddle initialization error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const openCheckout = async (options: CheckoutOptions) => {
    try {
      await paddleService.openCheckout(options)
    } catch (err: any) {
      error.value = err.message || 'Failed to open checkout'
      console.error('Paddle checkout error:', err)
      throw err
    }
  }

  return {
    isInitialized: readonly(isInitialized),
    isLoading: readonly(isLoading),
    error: readonly(error),
    init,
    openCheckout,
    paddleService
  }
}

// Auto-initialize Paddle when this module is imported
if (typeof window !== 'undefined') {
  paddleService.init().catch(console.error)
}

export default paddleService
