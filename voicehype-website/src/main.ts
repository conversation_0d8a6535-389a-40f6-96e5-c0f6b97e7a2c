// Import critical CSS first to prevent FOUC
import './assets/critical.css'
import './assets/main.css'
import './assets/dashboard-theme.css'
import './shimmer-fix.css'

// Import tailwind utilities to ensure they're included in the build
import 'tailwindcss/utilities.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Initialize Paddle
import './lib/paddle'

// Set body class immediately for best FOUC prevention
document.body.classList.add('bg-white', 'dark:bg-[#080814]')

// Initialize the app
const app = createApp(App)

// Use plugins
app.use(createPinia())
app.use(router)

// Mount the app
app.mount('#app')